#include "stm32f10x.h"                  // Device header
#include "Delay.h"
#include "OLED.h"
#include <stdlib.h>  // �������������

#define DINO_X 20    // ������X����̶�
#define DINO_Y 48    // ������Y����̶�
#define DINO_WIDTH 16
#define DINO_HEIGHT 16

#define CACTUS_WIDTH 8
#define CACTUS_HEIGHT 8
#define CACTUS_Y 48   // �����Ƶ�Y����̶�

#define SCREEN_WIDTH 128
#define MAX_CACTUS 2  // ���ͬʱ��ʾ������������

// �����ƽṹ��
typedef struct {
    int x;           // X����
    int active;      // �Ƿ񼤻�
    int size;        // ������ͣ�0=С������ڵ�����1=�������������µ��ӣ�
} Cactus;

int main(void)
{
    OLED_Init();
    
    // ��ʼ�������������
    srand(123);  // ʹ�ù̶����ӣ�Ҳ������ϵͳʱ�ӵ���Ϊ����
    
    // ��ʼ������������
    Cactus cacti[MAX_CACTUS] = {0};
    
    // ��������֡������
    uint8_t dinoAnimFrame = 0;
    uint8_t frameCounter = 0;
    
    // ��ǰ���������ͣ�0=С������ڵ�����1=�������������µ��ӣ�
    int currentCactusType = 0;
    
    // ��Ϸ��ѭ��
    while(1)
    {
        // ����
        OLED_Clear();
        
        // ��������Ч����ÿ5֡�л�һ�Σ�
        frameCounter++;
        if(frameCounter >= 5) {
            frameCounter = 0;
            dinoAnimFrame = !dinoAnimFrame; // ��0��1֮���л�
        }
        
        // ��ʾ���������ݶ���֡ѡ��ͼ��
        if(dinoAnimFrame == 0) {
            OLED_ShowImage(DINO_X, DINO_Y, DINO_WIDTH, DINO_HEIGHT, DinoJump);
        } else {
            // �����еڶ�֡���������û�У�����ʹ��ͬһ֡
            OLED_ShowImage(DINO_X, DINO_Y, DINO_WIDTH, DINO_HEIGHT, DinoJump);
        }
        
        // �����������߼�
        for (int i = 0; i < MAX_CACTUS; i++) {
            // ��������Ʋ���Ծ����һ������������������
            if (!cacti[i].active) {
                // 10%�ĸ���������������
                if (rand() % 100 < 10) {
                    cacti[i].active = 1;
                    cacti[i].x = SCREEN_WIDTH;  // ����Ļ�Ҳ࿪ʼ
                    
                    // ȷ����γ��ֵ�����������
                    if (i == 0) { // ֻ�ڵ�һ������������ʱ��������
                        currentCactusType = rand() % 2; // ����������ͣ�0=С������1=��������
                    }
                    cacti[i].size = currentCactusType;
                }
            } 
            // ��������ƻ�Ծ���ƶ�����ʾ��
            else {
                // �ƶ�������
                cacti[i].x -= 2;
                
                // ����������Ƴ���Ļ����Ϊ����Ծ
                if (cacti[i].x < -CACTUS_WIDTH) {
                    cacti[i].active = 0;
                } 
                // ������ʾ������
                else {
                    if (cacti[i].size == 0) {
                        // С������ - ��ʾ�ڵ�����
                        OLED_ShowImage(cacti[i].x, CACTUS_Y, CACTUS_WIDTH, CACTUS_HEIGHT, Cactus1);
                    } else {
                        // �������� - ����������ȫ���µ��ӣ�
                        OLED_ShowImage(cacti[i].x, CACTUS_Y - CACTUS_HEIGHT, CACTUS_WIDTH, CACTUS_HEIGHT, Cactus1);  // �ϲ�
                        OLED_ShowImage(cacti[i].x, CACTUS_Y, CACTUS_WIDTH, CACTUS_HEIGHT, Cactus1);                    // �²�
                    }
                }
            }
        }
        
        // ������ʾ
        OLED_Update();
        
        // ����֡��
        Delay_ms(50);
    }
}
