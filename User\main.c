#include "stm32f10x.h"                  // 设备头文件
#include "Delay.h"
#include "OLED.h"
#include "Key.h"                     // 按键头文件
#include <stdlib.h>                  // 用于随机数生成

#define DINO_X 20    // 恐龙初始X坐标
#define DINO_Y 48    // 恐龙初始Y坐标
#define DINO_WIDTH 16
#define DINO_HEIGHT 16

#define CACTUS_WIDTH 8
#define CACTUS_HEIGHT 8
#define CACTUS_Y 56   // 仙人掌底部Y坐标，与恐龙底部平齐（64-8=56）

#define SCREEN_WIDTH 128
#define MAX_CACTUS 2  // 最多同时显示的仙人掌数量
#define MIN_CACTUS_DISTANCE 40  // 两根仙人掌之间的最小距离

uint8_t is_jump = 0;
uint8_t KeyNum;
uint8_t gravity = 1;
int8_t velocity;
int dino_y = 48;                     // 恐龙初始Y坐标（地面位置）
uint8_t key_pressed_last = 0;        // 上一帧按键状态，用于检测按键按下边沿
uint8_t num = 0;
uint8_t is_paulsed = 0;

// 仙人掌结构体
typedef struct {
    int x;           // X坐标
    int active;      // 是否激活
    int size;        // 仙人掌类型：0=小仙人掌（在地面），1=大仙人掌（两个上下叠加）
} Cactus;

// 检查是否可以生成新仙人掌（确保与其他仙人掌有足够距离）
int canSpawnCactus(Cactus cacti[], int newX) {
    for (int i = 0; i < MAX_CACTUS; i++) {
        if (cacti[i].active) {
            // 计算与现有仙人掌的距离
            int distance = newX - cacti[i].x;
            if (distance < MIN_CACTUS_DISTANCE && distance > -MIN_CACTUS_DISTANCE) {
                return 0; // 距离太近，不能生成
            }
        }
    }
    return 1; // 可以生成
}


int main(void)
{
    OLED_Init();
    Key_Init();                      // 初始化按键

    // 初始化随机数种子
    srand(123);  // 使用固定种子，也可以替换为系统时间
    
    // 初始化仙人掌数组
    Cactus cacti[MAX_CACTUS] = {0};
    
    // 恐龙动画帧控制
    uint8_t dinoAnimFrame = 0;
    uint8_t frameCounter = 0;

    // 游戏主循环
    while(1)
    {
        // 非阻塞按键检测
        KeyNum = Key_GetState();

        // 检测按键按下边沿（从未按下到按下的瞬间）
        if (KeyNum == 2 && key_pressed_last == 0) {
            
            num++;
            if(num <= 2)
            {
            is_jump = 1;
            velocity = -6;  // 初始跳跃速度（向上为负）
            }
        }
        key_pressed_last = (KeyNum == 2) ? 1 : 0;  // 更新按键状态

         // 检测按键按下边沿（从未按下到按下的瞬间）
        if (KeyNum == 1 && key_pressed_last == 0) {
            
            if(is_paulsed)
                is_paulsed = 0;
            else
                is_paulsed = 1;
                        
          key_pressed_last = (KeyNum == 1) ? 1 : 0;  // 更新按键状态

        
        }
        
        if(is_paulsed)
        {
            OLED_Clear();
            OLED_ShowString(30, 30, "PAUSED",30);
            OLED_Update();
            Delay_ms(50);
        
        }
        
               
        // 处理跳跃物理
        if (is_jump == 1) {
            dino_y += velocity;
            velocity += gravity;  // 重力加速度

            // 检查是否回到地面
            if (dino_y >= 48) {
                dino_y = 48;
                is_jump = 0;   // 跳跃结束
                velocity = 0;  // 重置速度
                num = 0;
            }
        }
        
        // 清屏
        OLED_Clear();

        // 显示恐龙
        OLED_ShowImage(DINO_X, dino_y, DINO_WIDTH, DINO_HEIGHT, DinoJump);
        
        // 仙人掌处理逻辑
        for (int i = 0; i < MAX_CACTUS; i++) {

            // 第一步：处理未激活的仙人掌（尝试生成新仙人掌）
            if (!cacti[i].active) {
                // 10%的概率生成新仙人掌
                if (rand() % 100 < 10) {
                    int newX = SCREEN_WIDTH;  // 新仙人掌从屏幕右侧开始

                    // 检查是否可以在此位置生成仙人掌（确保间距足够）
                    if (canSpawnCactus(cacti, newX)) {
                        cacti[i].active = 1;
                        cacti[i].x = newX;

                        // 随机选择仙人掌类型
                        cacti[i].size = rand() % 2; // 0=小仙人掌，1=大仙人掌
                    }
                }
            }

            // 第二步：处理已激活的仙人掌（移动和显示）
            else {
                // 向左移动仙人掌
                cacti[i].x -= 2;

                // 第三步：检查仙人掌是否移出屏幕
                if (cacti[i].x < -CACTUS_WIDTH) {
                    cacti[i].active = 0;  // 移出屏幕，设为未激活
                }

                // 第四步：在屏幕上绘制仙人掌
                else {
                    if (cacti[i].size == 0) {
                        // 小仙人掌 - 单个，底部与恐龙平齐
                        OLED_ShowImage(cacti[i].x, CACTUS_Y, CACTUS_WIDTH, CACTUS_HEIGHT, Cactus1);
                    } else {
                        // 大仙人掌 - 两个完全上下叠加，底部与恐龙平齐
                        OLED_ShowImage(cacti[i].x, CACTUS_Y - CACTUS_HEIGHT, CACTUS_WIDTH, CACTUS_HEIGHT, Cactus1);  // 上部
                        OLED_ShowImage(cacti[i].x, CACTUS_Y, CACTUS_WIDTH, CACTUS_HEIGHT, Cactus1);                    // 下部
                    }
                }
            }
        }
        
        // 更新显示
        OLED_Update();
        
        // 帧延迟
        Delay_ms(50);
    }
}